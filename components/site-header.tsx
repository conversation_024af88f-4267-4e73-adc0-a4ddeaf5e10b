"use client"

import Image from "next/image"

import { useTranslations } from "next-intl"
import { Menu, Phone, Mail } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { site } from "@/lib/site"
import InquiryDialog from "@/components/inquiry-dialog"
import SocialLinks from "@/components/social-links"
import LocaleSwitcher from "@/components/locale-switcher"
import Link from "next/link";

export default function SiteHeader() {
  const t = useTranslations("nav")

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="hidden w-full bg-[#F6B1C3]/40 px-4 py-1 text-xs sm:flex items-center justify-center gap-4">
        <a href={site.phoneHref} className="inline-flex items-center gap-1 hover:underline">
          <Phone className="h-3.5 w-3.5" /> {site.phone}
        </a>
        <a href={site.emailHref} className="inline-flex items-center gap-1 hover:underline">
          <Mail className="h-3.5 w-3.5" /> {site.email}
        </a>
        <div className="ml-auto flex items-center gap-3">
          <SocialLinks />
          <LocaleSwitcher />
        </div>
      </div>
      <div className="mx-auto flex h-16 max-w-6xl items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <Link href="/" className="flex items-center gap-3">
            <div className="relative h-9 w-9 overflow-hidden rounded">
              <Image
                src="/images/logo-promenada-palic.png"
                alt="Promenada Palić logo"
                fill
                className="object-contain"
              />
            </div>
            <span className="font-semibold tracking-tight">{site.name}</span>
          </Link>
          <nav className="ml-6 hidden gap-6 text-sm font-medium sm:flex">
            <Link href="/" className="hover:underline underline-offset-4">
              {t("accommodation")}
            </Link>
            <Link href="/znamenitosti" className="hover:underline underline-offset-4">
              {t("attractions")}
            </Link>
            <Link href="/restorani" className="hover:underline underline-offset-4">
              {t("restaurants")}
            </Link>
            <Link href="/desavanja" className="hover:underline underline-offset-4">
              {t("events")}
            </Link>
          </nav>
        </div>
        <div className="hidden sm:block">
          <InquiryDialog roomName="Opšti upit" />
        </div>

        <Sheet>
          <SheetTrigger asChild>
            <Button size="icon" variant="outline" className="sm:hidden bg-transparent">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-80">
            <div className="mt-6 grid gap-4">
              <Link href="/" className="text-base font-medium">
                {t("accommodation")}
              </Link>
              <Link href="/znamenitosti" className="text-base font-medium">
                {t("attractions")}
              </Link>
              <Link href="/restorani" className="text-base font-medium">
                {t("restaurants")}
              </Link>
              <Link href="/desavanja" className="text-base font-medium">
                {t("events")}
              </Link>
              <InquiryDialog roomName="Opšti upit" />
              <div className="mt-2 grid gap-2 text-sm">
                <a href={site.phoneHref} className="inline-flex items-center gap-2">
                  <Phone className="h-4 w-4" /> {site.phone}
                </a>
                <a href={site.emailHref} className="inline-flex items-center gap-2">
                  <Mail className="h-4 w-4" /> {site.email}
                </a>
              </div>
              <LocaleSwitcher />
              <SocialLinks className="mt-2" />
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  )
}
