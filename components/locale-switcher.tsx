"use client"

import { usePathname } from "next/navigation"

import { locales, type Locale } from "@/i18n/config"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link";

export default function LocaleSwitcher({ className }: { className?: string }) {
  const pathname = usePathname() // includes current locale prefix
  const current = (pathname?.split("/")[1] || "sr") as Locale
  const other = (locales.find((l) => l !== current) || "en") as Locale

  const target = `/${other}${pathname?.replace(/^\/[a-z]{2}/, "") || ""}`

  return (
    <Button asChild variant="outline" className={className}>
      <Link href={target} prefetch>
        {other.toUpperCase()}
      </Link>
    </Button>
  )
}
