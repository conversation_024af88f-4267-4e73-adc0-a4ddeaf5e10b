"use client"

import { usePathname } from "next/navigation"

import { locales, type Locale } from "@/i18n/config"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link";

export default function LocaleSwitcher({ className }: { className?: string }) {
  const pathname = usePathname() // includes current locale prefix
  const segments = pathname?.split("/") || []
  const current = (segments[1] && locales.includes(segments[1] as Locale) ? segments[1] : "sr") as Locale
  const other = (locales.find((l) => l !== current) || "en") as Locale

  const pathWithoutLocale = pathname?.replace(/^\/[a-z]{2}/, "") || ""
  const target = `/${other}${pathWithoutLocale}`

  return (
    <Button asChild variant="outline" className={className}>
      <Link href={target} prefetch>
        {other.toUpperCase()}
      </Link>
    </Button>
  )
}
