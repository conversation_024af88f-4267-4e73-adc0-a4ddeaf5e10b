"use client"

import Image from "next/image"
import Link from "next/link"
import { <PERSON>, <PERSON>ge<PERSON><PERSON><PERSON>, MapPin } from 'lucide-react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import InquiryDialog from "@/components/inquiry-dialog"

type Props = {
  slug?: string
  title?: string
  image?: string
  alt?: string
  price?: string
  capacity?: string
}

export default function RoomCard({
  slug = "smestaj",
  title = "Apartman",
  image = "/comfortable-hotel-room.png",
  alt = "Soba",
  price = "50€ / noć",
  capacity = "2 gosta",
}: Props) {
  return (
    <Card className="overflow-hidden">
      <Link href={`/smestaj/${slug}`} className="group block">
        <div className="relative aspect-[4/3]">
          <Image
            src={image || "/placeholder.svg?height=300&width=400&query=room%20photo"}
            alt={alt}
            fill
            sizes="(min-width: 1024px) 33vw, (min-width: 640px) 50vw, 100vw"
            className="object-cover transition-transform group-hover:scale-[1.02]"
          />
        </div>
      </Link>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span>{title}</span>
          <Badge variant="secondary" className="gap-1">
            <BadgeCheck className="h-3.5 w-3.5" /> Provereno
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="grid gap-3 text-sm">
        <div className="flex items-center gap-3 text-muted-foreground">
          <Users className="h-4 w-4" /> {capacity}
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="font-medium">{price}</div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/smestaj/${slug}`} className="inline-flex items-center gap-2">
              <MapPin className="h-4 w-4" /> Detalji
            </Link>
          </Button>
          <InquiryDialog roomName={title} />
        </div>
      </CardFooter>
    </Card>
  )
}
