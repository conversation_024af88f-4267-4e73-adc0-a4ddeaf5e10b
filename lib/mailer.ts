import { site } from "@/lib/site"

export async function sendOwnerAndGuestEmails(opts: {
  guestEmail: string
  guestName: string
  roomName: string
  details: string
}) {
  const key = process.env.RESEND_API_KEY
  const from = process.env.RESEND_FROM
  if (!key || !from) {
    console.warn("Email disabled: missing RESEND_API_KEY or RESEND_FROM")
    return { sent: false, reason: "missing-env" }
  }

  const { Resend } = await import("resend")
  const resend = new Resend(key)

  const ownerSubject = `Novi upit — ${opts.roomName} — ${opts.guestName}`
  const ownerText = `Stigao je novi upit.\n\n${opts.details}\n\nKontakt gosta: ${opts.guestEmail}\n`
  const guestSubject = `Promenada Palić — potvrda prijema upita`
  const guestText =
    `Poštovani ${opts.guestName},\n\nVaš upit se obrađu<PERSON> — uskoro ćemo Vas kontaktirati.\n\n` +
    `Hvala na interesovanju za ${opts.roomName} u ${site.name}.\n\nSrdačan pozdrav,\n${site.name}\n${site.phone}\n${site.email}\n`

  try {
    await Promise.all([
      resend.emails.send({
        from,
        to: site.email,
        subject: ownerSubject,
        text: ownerText
      }),
      resend.emails.send({
        from,
        to: opts.guestEmail,
        subject: guestSubject,
        text: guestText
      })
    ])
    return { sent: true }
  } catch (e) {
    console.error("Resend error:", e)
    return { sent: false, reason: "resend-error" }
  }
}
