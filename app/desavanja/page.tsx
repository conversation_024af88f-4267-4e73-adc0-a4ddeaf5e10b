import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { ExternalLink, ArrowRight } from 'lucide-react'

import SiteHeader from "@/components/site-header"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export const metadata: Metadata = {
  title: "Aktuelna dešavanja — Palić i Subotica",
  description:
    "Pratite aktuelna dešavanja bez ručnog unosa. Linkovi vode na zvanične stranice i pretrage događaja za Palić i Suboticu.",
  alternates: { canonical: "/desavanja" },
}

const sources = [
  {
    name: "Google pretraga — događaji Palić i Subotica",
    description: "Opšta pretraga aktuelnih događaja (koncerti, izložbe, manifestacije).",
    href: "https://www.google.com/search?q=doga%C4%91aji+<PERSON><PERSON>%C4%87+Subotica"
  },
  {
    name: "Facebook Events — Subotica i Palić",
    description: "Pregled javnih događaja prijavljenih na Facebooku.",
    href: "https://www.facebook.com/events/search/?q=Subotica%20Pali%C4%87"
  },
  {
    name: "Google Maps — Events near Palić",
    description: "Pregled događaja u okolini kroz Maps listinge.",
    href: "https://www.google.com/maps/search/Events+near+Pali%C4%87"
  },
  {
    name: "Vinarije i degustacije — pretraga",
    description: "Aktuelna dešavanja u vinarijama (degustacije, turе).",
    href: "https://www.google.com/search?q=vinarija+Subotica+Pali%C4%87+degu%C5%A1tacija"
  },
  {
    name: "Kulturni program — Subotica (pretraga)",
    description: "Pozorište, galerije, bioskop i kulturni centri.",
    href: "https://www.google.com/search?q=kultura+Subotica+program"
  }
]

export default function Page() {
  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić jezero — zalazak sunca"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <Badge className="bg-[#0FA3B1]/90">Dešavanja</Badge>
          <h1 className="mt-3 text-3xl font-bold tracking-tight sm:text-4xl">
            Aktuelna dešavanja — Palić i Subotica
          </h1>
          <p className="mt-2 max-w-2xl text-white/90">
            Automatski ažurirano — vodimo vas direktno na zvanične izvore i pretrage događaja. Bez prepisivanja.
          </p>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 sm:grid-cols-2">
          {sources.map((s) => (
            <Card key={s.href}>
              <CardHeader>
                <CardTitle>{s.name}</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-3">
                <p className="text-sm text-muted-foreground">{s.description}</p>
                <div>
                  <Button asChild className="bg-[#0FA3B1] hover:bg-[#0C8995]">
                    <a href={s.href} target="_blank" rel="noreferrer" className="inline-flex items-center gap-2">
                      Otvori izvor <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-8 rounded-md border p-4">
          <div className="font-medium">Saveti</div>
          <p className="mt-1 text-sm text-muted-foreground">
            Možete zapratiti stranice organizatora kako biste dobijali obaveštenja o novim događajima.
          </p>
          <div className="mt-3">
            <Button asChild variant="outline">
              <a href="https://www.google.com/search?q=manifestacije+Pali%C4%87+Subotica" target="_blank" rel="noreferrer" className="inline-flex items-center gap-2">
                Manifestacije — pretraga <ArrowRight className="h-4 w-4" />
              </a>
            </Button>
          </div>
        </div>
      </section>
    </main>
  )
}
