import type { ReactNode } from "react"
import { NextIntlClientProvider } from "next-intl"
import { notFound } from "next/navigation"
import { locales, type Locale } from "@/i18n/config"
import AnalyticsProvider from "@/components/analytics-provider"
import "../globals.css"
import { Suspense } from "react"

async function getMessages(locale: Locale) {
  try {
    const messages = (await import(`@/messages/${locale}.json`)).default
    return messages
  } catch {
    return null
  }
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export default async function LocaleLayout({
  params,
  children,
}: {
  params: { locale: Locale }
  children: ReactNode
}) {
  const { locale } = params
  if (!locales.includes(locale)) notFound()

  const messages = await getMessages(locale)
  if (!messages) notFound()

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Suspense fallback={<div>Loading...</div>}>
            {children}
            <AnalyticsProvider />
          </Suspense>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
