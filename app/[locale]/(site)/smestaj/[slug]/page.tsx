import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import rooms from "@/data/rooms.json"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { MapPin, ArrowLeft } from 'lucide-react'
import SiteHeader from "@/components/site-header"
import InquiryDialog from "@/components/inquiry-dialog"
import TrackLink from "@/components/track-link"

type Room = typeof rooms extends (infer R)[] ? R : never

export function generateStaticParams() {
  return (rooms as any[]).map((r) => ({ slug: r.slug }))
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const { slug } = await params
  const room = (rooms as any[]).find((r) => r.slug === slug)
  if (!room) return { title: "<PERSON><PERSON><PERSON><PERSON><PERSON>" }
  return {
    title: `${room.title} — <PERSON><PERSON><PERSON>`,
    description: room.shortDescription,
    alternates: { canonical: `/smestaj/${room.slug}` }
  }
}

export default async function Page({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params
  const room = (rooms as any[]).find((r) => r.slug === slug)
  if (!room) return <div>Smeštaj nije pronađen</div>

  const gmaps = `https://www.google.com/maps/search/?api=1&query=${room.coords.lat},${room.coords.lng}`

  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src={room.images?.[0] || "/placeholder.svg"}
            alt={room.primaryImageAlt || room.title}
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <div className="flex items-center gap-3 mb-4">
            <Button asChild variant="outline" size="sm" className="bg-white/10 border-white/40 text-white hover:bg-white/20">
              <Link href="/" className="inline-flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" /> Nazad
              </Link>
            </Button>
            <Badge className="bg-[#0FA3B1]/90">{room.capacity}</Badge>
          </div>
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {room.title}
          </h1>
          <p className="mt-2 max-w-2xl text-white/90">
            {room.shortDescription}
          </p>
          <div className="mt-4 flex items-center gap-2 text-white/90">
            <MapPin className="h-5 w-5" />
            <span>{room.address}</span>
          </div>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 lg:grid-cols-2">
          <div className="grid gap-4">
            {room.images?.map((img: string, i: number) => (
              <div key={i} className="relative aspect-[16/9] overflow-hidden rounded-lg">
                <Image
                  src={img}
                  alt={`${room.title} - slika ${i + 1}`}
                  fill
                  sizes="(min-width: 1024px) 50vw, 100vw"
                  className="object-cover"
                />
              </div>
            ))}
          </div>

          <div className="grid gap-4 h-fit">
            <Card>
              <CardContent className="p-6">
                <div className="grid gap-4">
                  <div>
                    <div className="text-2xl font-bold text-[#0FA3B1]">{room.price}</div>
                    <div className="text-sm text-muted-foreground">za {room.capacity}</div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="font-medium mb-2">Opis</h3>
                    <p className="text-sm text-muted-foreground">{room.description}</p>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Sadržaj</h3>
                    <div className="flex flex-wrap gap-2">
                      {room.amenities?.map((amenity: string) => (
                        <Badge key={amenity} variant="secondary">{amenity}</Badge>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div className="grid gap-3">
                    <InquiryDialog room={room.title} />
                    <Button asChild variant="outline">
                      <TrackLink
                        href={gmaps}
                        target="_blank"
                        rel="noreferrer"
                        className="inline-flex items-center gap-2"
                        event="map_open"
                        params={{ type: "accommodation", name: room.title }}
                      >
                        <MapPin className="h-4 w-4" /> Prikaži na mapi
                      </TrackLink>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}
