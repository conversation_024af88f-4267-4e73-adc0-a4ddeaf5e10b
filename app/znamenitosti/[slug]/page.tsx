import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { MapPin, ArrowLeft, Sparkles } from 'lucide-react'

import attractions from "@/data/attractions.json"
import SiteHeader from "@/components/site-header"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import TrackLink from "@/components/track-link"

type Attr = typeof attractions extends (infer R)[] ? R : never

export function generateStaticParams() {
  return (attractions as any[]).map((a) => ({ slug: a.slug }))
}

export function generateMetadata({ params }: { params: { slug: string } }): Metadata {
  const a = (attractions as any[]).find((x) => x.slug === params.slug)
  if (!a) return { title: "Znamenitost" }
  return {
    title: `${a.title} — Znamenitosti`,
    description: a.short,
    alternates: { canonical: `/znamenitosti/${a.slug}` },
    openGraph: {
      title: a.title,
      description: a.short,
      images: [{ url: a.image || "/opengraph-image.png", width: 1200, height: 630 }]
    }
  }
}

export default function Page({ params }: { params: { slug: string } }) {
  const a = (attractions as any[]).find((x) => x.slug === params.slug) as Attr | undefined
  if (!a) {
    return (
      <main>
        <SiteHeader />
        <div className="mx-auto max-w-6xl px-4 py-10">
          <p>Nije pronađeno.</p>
        </div>
      </main>
    )
  }

  const mapsEmbed = `https://www.google.com/maps?q=${a.coords.lat},${a.coords.lng}&hl=sr&z=16&output=embed`
  const mapsLink = `https://www.google.com/maps/search/?api=1&query=${a.mapsQuery}`

  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić — jezero u zalasku"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-10 text-white">
          <Button asChild variant="outline" className="bg-white/10 border-white/40 text-white hover:bg-white/20">
            <Link href="/znamenitosti" className="inline-flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" /> Nazad na sve znamenitosti
            </Link>
          </Button>
          <h1 className="mt-4 text-3xl font-bold tracking-tight">{a.title}</h1>
          <p className="mt-2 max-w-2xl text-white/90">{a.short}</p>
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
        <div className="grid gap-6 lg:grid-cols-2">
          <div className="grid gap-4">
            <div className="relative aspect-[16/9] overflow-hidden rounded-lg">
              <Image
                src={a.image || "/placeholder.svg?height=500&width=800&query=znamenitost"}
                alt={a.title}
                fill
                sizes="(min-width: 1024px) 50vw, 100vw"
                className="object-cover"
              />
            </div>
            <article className="prose prose-sm sm:prose-base max-w-none">
              <h2>Istorijat</h2>
              <p>{a.history}</p>
            </article>
            {"story" in a && (a as any).story ? (
              <article className="prose prose-sm sm:prose-base max-w-none">
                <h3 className="inline-flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-[#0FA3B1]" /> Interesantno
                </h3>
                <p>{(a as any).story}</p>
              </article>
            ) : null}
          </div>

          <div className="grid gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="mb-2 flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-[#0FA3B1]" />
                  <span className="font-medium">Kako doći</span>
                </div>
                <div className="relative h-64 overflow-hidden rounded-md">
                  <iframe
                    title={`Mapa — ${a.title}`}
                    src={mapsEmbed}
                    className="h-full w-full border-0"
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  />
                </div>
                <div className="mt-3">
                  <Button asChild>
                    <TrackLink
                      href={mapsLink}
                      target="_blank"
                      rel="noreferrer"
                      event="map_open"
                      params={{ type: "attraction", name: a.title }}
                    >
                      Otvori u Google Maps
                    </TrackLink>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}
