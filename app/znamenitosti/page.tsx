import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import { MapPin, Landmark, Info } from 'lucide-react'

import attractions from "@/data/attractions.json"
import SiteHeader from "@/components/site-header"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Znamenitosti Palića i Subotice — vodič, istorijat i mapa",
  description:
    "Istorijat Palića i Subotice, uz vodič kroz vile, štrandove, pozornicu, vodotoranj, sinagogu, katedralu, plavu fontanu, gimnaziju i druge znamenitosti. Svaka ima opis i uputstvo do lokacije.",
  alternates: { canonical: "/znamenitosti" }
}

const groups = {
  "Palić": (attractions as any[]).filter((a) => a.city === "Palić"),
  "Subotica": (attractions as any[]).filter((a) => a.city === "Subotica")
}

export default function Page() {
  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Palić — zalazak sunca nad jezerom"
            fill
            sizes="100vw"
            className="object-cover brightness-[0.6]"
            priority
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-12 sm:py-16 text-white">
          <Badge className="bg-[#0FA3B1]/90">Vodič</Badge>
          <h1 className="mt-3 text-3xl font-bold tracking-tight sm:text-4xl">
            Znamenitosti Palića i Subotice
          </h1>
          <p className="mt-2 max-w-3xl text-white/90">
            Istražite arhitekturu secesije, istorijske vile, parkove i sakralne objekte. Kliknite na znamenitost za opis, zanimljivosti i uputstvo do lokacije.
          </p>
        </div>
      </section>

      {/* Istorijat — Palić i Subotica */}
      <section className="mx-auto max-w-6xl px-4 pt-6 sm:pt-10">
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-[#0FA3B1]" /> Kratki istorijat Palića
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-muted-foreground">
              <p>
                Palić je poznat kao banjsko i izletišno mesto od 19. veka, uz legendu o jezeru nastalom od pastirovih suza.
                Početkom 20. veka doživljava procvat: grade se Velika terasa, štrandovi, vodotoranj i niz secesijskih vila.
                Mineralno blato i klimatoterapija privlače goste iz regiona, a tradiciju sporta neguju teniski, jedriličarski i veslački klubovi.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-[#0FA3B1]" /> Kratki istorijat Subotice
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-muted-foreground">
              <p>
                Subotica se u pisanim izvorima pominje u srednjem veku, status slobodnog kraljevskog grada dobija 1779.
                Početkom 1900‑ih postaje centar secesijske arhitekture — Gradska kuća, sinagoga i reprezentativne palate.
                Prva tramvajska veza sa Palićem krajem 19. veka dodatno je učvrstila ulogu Palića kao letovališta.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {Object.entries(groups).map(([city, list]) => (
        <section key={city} className="mx-auto max-w-6xl px-4 py-8 sm:py-12">
          <div className="mb-4 flex items-center gap-2">
            <Landmark className="h-5 w-5 text-[#0FA3B1]" />
            <h2 className="text-2xl font-semibold tracking-tight">{city}</h2>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {list.map((item: any) => {
              const maps = `https://www.google.com/maps/search/?api=1&query=${item.mapsQuery}`
              return (
                <Card key={item.slug} className="overflow-hidden">
                  <Link href={`/znamenitosti/${item.slug}`} className="group block">
                    <div className="relative aspect-[16/9]">
                      <Image
                        src={item.image || "/placeholder.svg?height=300&width=500&query=attraction"}
                        alt={item.title}
                        fill
                        sizes="(min-width: 1024px) 33vw, (min-width: 640px) 50vw, 100vw"
                        className="object-cover transition-transform group-hover:scale-[1.02]"
                      />
                    </div>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{item.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-sm text-muted-foreground">{item.short}</p>
                      <div className="mt-3 inline-flex items-center gap-2 text-xs text-[#0FA3B1]">
                        <MapPin className="h-4 w-4" />
                        <span>Prikaži detalje i uputstvo</span>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              )
            })}
          </div>
        </section>
      ))}
    </main>
  )
}
