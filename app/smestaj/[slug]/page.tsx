import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import rooms from "@/data/rooms.json"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { MapPin, ArrowLeft } from 'lucide-react'
import SiteHeader from "@/components/site-header"
import InquiryDialog from "@/components/inquiry-dialog"
import TrackLink from "@/components/track-link"

type Room = typeof rooms extends (infer R)[] ? R : never

export function generateStaticParams() {
  return (rooms as any[]).map((r) => ({ slug: r.slug }))
}

export function generateMetadata({ params }: { params: { slug: string } }): Metadata {
  const room = (rooms as any[]).find((r) => r.slug === params.slug)
  if (!room) return { title: "<PERSON><PERSON><PERSON><PERSON><PERSON>" }
  return {
    title: `${room.title} — <PERSON><PERSON><PERSON>`,
    description: room.shortDescription,
    openGraph: {
      title: `${room.title} — Promenada <PERSON>`,
      description: room.shortDescription,
      images: [{ url: room.images?.[0] || "/opengraph-image.png", width: 1200, height: 630 }],
    },
    alternates: { canonical: `/smestaj/${room.slug}` },
  }
}

export default function Page({ params }: { params: { slug: string } }) {
  const room = (rooms as any[]).find((r) => r.slug === params.slug) as Room | undefined
  if (!room) {
    return (
      <main className="mx-auto max-w-6xl px-4 py-10">
        <SiteHeader />
        <p>Nije pronađeno.</p>
      </main>
    )
  }

  const mapsEmbed = `https://www.google.com/maps?q=${room.coords.lat},${room.coords.lng}&hl=sr&z=16&output=embed`
  const mapsLink = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
    `${room.address} Palić`
  )}`

  return (
    <main>
      <SiteHeader />
      <section className="mx-auto max-w-6xl px-4 py-6">
        <div className="mb-4">
          <Button asChild variant="ghost">
            <Link href="/" className="inline-flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" /> Nazad na listu smeštaja
            </Link>
          </Button>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          <div className="grid gap-3">
            <div className="relative aspect-[4/3] overflow-hidden rounded-lg">
              <Image
                src={room.images?.[0] || "/placeholder.svg?height=600&width=800&query=room%20photo"}
                alt={room.primaryImageAlt || room.title}
                fill
                sizes="(min-width: 1024px) 50vw, 100vw"
                className="object-cover"
              />
            </div>
            {room.images?.slice(1).map((src: string, idx: number) => (
              <div key={idx} className="relative aspect-[4/3] overflow-hidden rounded-lg">
                <Image
                  src={src || "/placeholder.svg"}
                  alt={`${room.title} — slika ${idx + 2}`}
                  fill
                  sizes="(min-width: 1024px) 50vw, 100vw"
                  className="object-cover"
                />
              </div>
            ))}
          </div>

          <div className="grid content-start gap-4">
            <div>
              <h1 className="text-2xl font-semibold tracking-tight">{room.title}</h1>
              <p className="mt-2 text-muted-foreground">{room.shortDescription}</p>
            </div>

            <div className="flex flex-wrap gap-2">
              {(room.amenities || []).map((a: string) => (
                <Badge key={a} variant="secondary" className="border-[#0FA3B1] text-[#0FA3B1]">
                  {a}
                </Badge>
              ))}
            </div>

            <Separator />
            <div className="flex items-center justify-between">
              <div className="text-lg font-medium">{room.price}</div>
              <InquiryDialog roomName={room.title} />
            </div>

            <Card className="mt-2">
              <CardContent className="p-4">
                <div className="mb-2 flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-[#0FA3B1]" />
                  <span className="font-medium">{room.address}</span>
                </div>
                <div className="relative h-64 overflow-hidden rounded-md">
                  <iframe
                    title={`Mapa — ${room.title}`}
                    src={mapsEmbed}
                    className="h-full w-full border-0"
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  />
                </div>
                <div className="mt-3">
                  <Button asChild variant="outline">
                    <TrackLink
                      href={mapsLink}
                      target="_blank"
                      rel="noreferrer"
                      event="map_open"
                      params={{ type: "room", name: room.title }}
                    >
                      Otvori u Google Maps
                    </TrackLink>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </main>
  )
}
