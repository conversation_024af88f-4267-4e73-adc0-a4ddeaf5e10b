import Image from "next/image"
import Link from "next/link"
import type { Metadata } from "next"
import { MapPin, Star, Wifi, BedDouble, Users, ChevronRight, Phone, Mail } from "lucide-react"
import SocialLinks from "@/components/social-links"
import { redirect } from "next/navigation"
import { defaultLocale } from "@/i18n/config"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

import SiteHeader from "@/components/site-header"
import RoomCard from "@/components/room-card"
import rooms from "@/data/rooms.json"
import { site } from "@/lib/site"

export const metadata: Metadata = {
  title: "Promenada <PERSON>lić — smeštaj uz jezero | Apartmani i sobe",
  description:
    "Smeštaj na Paliću pored jezera: moderni apartmani i sobe, besplatan Wi‑Fi i parking. Idealan odmor za parove i porodice.",
  keywords: ["s<PERSON><PERSON><PERSON><PERSON>", "apartmani Palić", "sobe <PERSON>", "jezero <PERSON> smeštaj", "Promenada <PERSON>"],
  openGraph: {
    title: "Promenada Palić — smeštaj uz jezero | Apartmani i sobe",
    description: "Udoban smeštaj na Paliću uz jezero. Odlična lokacija, Wi‑Fi i parking. Idealno za parove i porodice.",
    images: [{ url: site.ogImage, width: 1200, height: 630 }],
    type: "website",
    locale: "sr_RS",
  },
  alternates: { canonical: "/" },
  robots: { index: true, follow: true },
}

// JSON-LD
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Hotel",
  name: site.name,
  description: "Smeštaj na Paliću uz jezero. Apartmani i sobe sa Wi‑Fi i parkingom. Blizu šetališta i Zoo vrta.",
  address: {
    "@type": "PostalAddress",
    addressLocality: "Palić",
    addressCountry: "RS",
  },
  url: "https://example.com",
  telephone: site.phone,
}

export default function Page() {
  redirect(`/${defaultLocale}`)

  return (
    <main>
      <SiteHeader />
      <section className="relative">
        <div className="absolute inset-0 -z-10">
          <Image
            src="/images/hero-palic.jpg"
            alt="Zalazak sunca na jezeru Palić sa jedrilicom i molovima"
            fill
            priority
            sizes="100vw"
            className="object-cover brightness-[0.6] saturate-110"
          />
        </div>
        <div className="mx-auto max-w-6xl px-4 py-16 sm:py-20 lg:py-28 text-white">
          <div className="max-w-2xl">
            <div className="inline-flex items-center gap-3 rounded-full bg-[#0FA3B1]/90 px-3 py-1 text-sm">
              <Star className="h-4 w-4 text-white" />
              <span>Ocena gostiju 4.9/5</span>
            </div>
            <h1 className="mt-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {site.name} — smeštaj uz jezero Palić
            </h1>
            <p className="mt-3 text-white/90">
              Moderni apartmani i sobe na par koraka od obale. Besplatan Wi‑Fi, parking i mirno okruženje.
            </p>
            <div className="mt-6 flex flex-wrap gap-3">
              <Button asChild size="lg" className="bg-[#0FA3B1] hover:bg-[#0C8995]">
                <a href="#sobe">Pogledajte smeštaj</a>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="bg-white/10 border-white/40 text-white hover:bg-white/20"
              >
                <Link href="/znamenitosti" className="inline-flex items-center gap-2">
                  Znamenitosti i ruta <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="mt-6 flex flex-wrap items-center gap-6 text-white/90">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                <span>50 m od obale jezera Palić</span>
              </div>
              <a
                href={site.phoneHref}
                className="flex items-center gap-2 underline decoration-white/40 underline-offset-4"
              >
                <Phone className="h-5 w-5" /> {site.phone}
              </a>
              <a
                href={site.emailHref}
                className="flex items-center gap-2 underline decoration-white/40 underline-offset-4"
              >
                <Mail className="h-5 w-5" /> {site.email}
              </a>
            </div>
          </div>
        </div>
      </section>

      <section id="sobe" className="mx-auto max-w-6xl px-4 py-10 sm:py-12">
        <div className="mb-6 sm:mb-8">
          <h2 className="text-2xl font-semibold tracking-tight">Sobe i apartmani</h2>
          <p className="text-muted-foreground">
            Izaberite idealan smeštaj — detalji svake jedinice i mapa lokacije dostupni su na stranici smeštaja.
          </p>
        </div>
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {(rooms as any[]).map((room) => (
            <RoomCard
              key={room.slug}
              slug={room.slug}
              title={room.title}
              image={room.images?.[0]}
              alt={room.primaryImageAlt}
              price={room.price}
              capacity={room.capacity}
            />
          ))}
        </div>
      </section>

      <section className="mx-auto max-w-6xl px-4 pb-12">
        <Card>
          <CardHeader>
            <CardTitle>Zašto baš Palić?</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-4 sm:grid-cols-3">
            <div className="flex items-start gap-3">
              <Wifi className="h-5 w-5 mt-0.5 text-[#0FA3B1]" />
              <div>
                <div className="font-medium">Brz Wi‑Fi i parking</div>
                <div className="text-sm text-muted-foreground">Savršeno za rad na daljinu ili vikend beg.</div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <BedDouble className="h-5 w-5 mt-0.5 text-[#F17C9A]" />
              <div>
                <div className="font-medium">Udobne sobe</div>
                <div className="text-sm text-muted-foreground">Moderno uređenje i pažnja posvećena detaljima.</div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Users className="h-5 w-5 mt-0.5 text-[#6BCBDB]" />
              <div>
                <div className="font-medium">Za parove i porodice</div>
                <div className="text-sm text-muted-foreground">Od studia do porodičnog apartmana.</div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="justify-between">
            <div className="text-sm text-muted-foreground">
              Planirate obilazak? Pogledajte naš vodič kroz znamenitosti.
            </div>
            <Button asChild variant="secondary" className="border-[#0FA3B1] text-[#0FA3B1]">
              <Link href="/znamenitosti">Plan obilaska</Link>
            </Button>
          </CardFooter>
        </Card>
      </section>

      <Separator />
      <footer className="mx-auto max-w-6xl px-4 py-8 text-sm text-muted-foreground">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
          <div>
            © {new Date().getFullYear()} {site.name} — Sva prava zadržana.
          </div>
          <div className="flex flex-wrap gap-4">
            <Link className="underline underline-offset-2" href="/znamenitosti">
              Znamenitosti
            </Link>
            <Link className="underline underline-offset-2" href="/restorani">
              Restorani u blizini
            </Link>
            <Link className="underline underline-offset-2" href="/desavanja">
              Dešavanja
            </Link>
            <a
              className="underline underline-offset-2"
              href="https://maps.google.com/?q=Palic"
              target="_blank"
              rel="noreferrer"
            >
              Lokacija
            </a>
            <SocialLinks />
          </div>
        </div>
      </footer>

      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
    </main>
  )
}
